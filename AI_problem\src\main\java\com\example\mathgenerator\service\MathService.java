package com.example.mathgenerator.service;

import org.springframework.stereotype.Service;
import java.util.Random;

@Service
public class MathService {
    private final Random random = new Random();
    
    public String generateProblem(String type) {
        switch (type.toLowerCase()) {
            case "addition":
                return generateAdditionProblem();
            case "subtraction":
                return generateSubtractionProblem();
            case "multiplication":
                return generateMultiplicationProblem();
            case "division":
                return generateDivisionProblem();
            default:
                return "Unsupported problem type. Supported types: addition, subtraction, multiplication, division";
        }
    }
    
    private String generateAdditionProblem() {
        int a = random.nextInt(100) + 1;
        int b = random.nextInt(100) + 1;
        return String.format("%d + %d = ?", a, b);
    }
    
    private String generateSubtractionProblem() {
        int a = random.nextInt(100) + 1;
        int b = random.nextInt(a) + 1; // Ensure positive result
        return String.format("%d - %d = ?", a, b);
    }
    
    private String generateMultiplicationProblem() {
        int a = random.nextInt(12) + 1;
        int b = random.nextInt(12) + 1;
        return String.format("%d × %d = ?", a, b);
    }
    
    private String generateDivisionProblem() {
        int b = random.nextInt(12) + 1;
        int result = random.nextInt(12) + 1;
        int a = b * result; // Ensure clean division
        return String.format("%d ÷ %d = ?", a, b);
    }
}
