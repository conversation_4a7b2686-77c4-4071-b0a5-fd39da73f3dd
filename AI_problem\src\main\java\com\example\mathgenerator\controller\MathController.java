package com.example.mathgenerator.controller;

import com.example.mathgenerator.service.MathService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/math")
public class MathController {
    private final MathService mathService;

    @Autowired
    public MathController(MathService mathService) {
        this.mathService = mathService;
    }

    @GetMapping("/generate")
    public String generateProblem(@RequestParam String type) {
        return mathService.generateProblem(type);
    }
}