package com.example.mathgenerator.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

public class MathServiceTest {
    private MathService mathService;
    
    @BeforeEach
    void setUp() {
        mathService = new MathService();
    }
    
    @Test
    void testGenerateAdditionProblem() {
        String result = mathService.generateProblem("addition");
        assertNotNull(result);
        assertTrue(result.contains("+"));
        assertTrue(result.contains("="));
        assertTrue(result.contains("?"));
    }
    
    @Test
    void testGenerateSubtractionProblem() {
        String result = mathService.generateProblem("subtraction");
        assertNotNull(result);
        assertTrue(result.contains("-"));
        assertTrue(result.contains("="));
        assertTrue(result.contains("?"));
    }
    
    @Test
    void testGenerateMultiplicationProblem() {
        String result = mathService.generateProblem("multiplication");
        assertNotNull(result);
        assertTrue(result.contains("×"));
        assertTrue(result.contains("="));
        assertTrue(result.contains("?"));
    }
    
    @Test
    void testGenerateDivisionProblem() {
        String result = mathService.generateProblem("division");
        assertNotNull(result);
        assertTrue(result.contains("÷"));
        assertTrue(result.contains("="));
        assertTrue(result.contains("?"));
    }
    
    @Test
    void testUnsupportedProblemType() {
        String result = mathService.generateProblem("invalid");
        assertTrue(result.contains("Unsupported problem type"));
    }
}
